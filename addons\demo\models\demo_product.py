"""
Demo Product model
"""
import sys
import os

# Add the ERP core to Python path
erp_path = os.path.join(os.path.dirname(__file__), '..', '..', '..')
if erp_path not in sys.path:
    sys.path.insert(0, erp_path)

from erp.models.base import BaseModel
from erp.fields import Char, Text, Boolean, Selection, Integer, Float


class DemoProduct(BaseModel):
    """Demo product model"""
    
    _name = 'demo.product'
    _description = 'Demo Product'
    _table = 'demo_product'
    
    # Override name field
    name = Char(string='Product Name', required=True, help='Name of the product')
    
    # Product-specific fields
    code = Char(string='Product Code', size=20, required=True, help='Unique product code')
    description = Text(string='Description', help='Detailed product description')
    
    product_type = Selection([
        ('consumable', 'Consumable'),
        ('service', 'Service'),
        ('stockable', 'Stockable Product'),
    ], string='Product Type', default='stockable', required=True)
    
    category = Selection([
        ('electronics', 'Electronics'),
        ('clothing', 'Clothing'),
        ('books', 'Books'),
        ('food', 'Food & Beverages'),
        ('other', 'Other'),
    ], string='Category', default='other')
    
    active = Boolean(string='Active', default=True, help='Whether the product is active')
    can_be_sold = Boolean(string='Can be Sold', default=True)
    can_be_purchased = Boolean(string='Can be Purchased', default=True)
    
    # Pricing
    list_price = Float(string='Sales Price', default=0.0, help='Base sales price')
    cost_price = Float(string='Cost Price', default=0.0, help='Product cost price')
    
    # Inventory
    qty_available = Integer(string='Quantity On Hand', default=0, readonly=True)
    qty_reserved = Integer(string='Reserved Quantity', default=0, readonly=True)
    
    # Weight and dimensions
    weight = Float(string='Weight (kg)', default=0.0)
    volume = Float(string='Volume (m³)', default=0.0)
    
    def get_available_qty(self):
        """Get available quantity (on hand - reserved)"""
        return (self.qty_available or 0) - (self.qty_reserved or 0)
    
    def update_stock(self, quantity_change):
        """Update stock quantity"""
        new_qty = (self.qty_available or 0) + quantity_change
        self.write({'qty_available': max(0, new_qty)})
    
    def calculate_margin(self):
        """Calculate profit margin percentage"""
        if not self.cost_price or self.cost_price == 0:
            return 0.0
        return ((self.list_price - self.cost_price) / self.cost_price) * 100
    
    @classmethod
    def get_low_stock_products(cls, threshold=10):
        """Get products with low stock"""
        # This would typically use database search
        return cls.search([('qty_available', '<', threshold), ('active', '=', True)])
    
    def set_price(self, new_price):
        """Set new sales price"""
        self.write({'list_price': new_price})
