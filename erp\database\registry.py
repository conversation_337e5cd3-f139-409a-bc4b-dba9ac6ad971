"""
Database registry for managing multiple databases
"""
from typing import Dict, Optional
from .connection import DatabaseManager


class DatabaseRegistry:
    """Registry for managing multiple database connections"""
    
    _databases: Dict[str, DatabaseManager] = {}
    _current_db: Optional[str] = None
    
    @classmethod
    def get_database(cls, db_name: str) -> DatabaseManager:
        """Get database manager for given database name"""
        if db_name not in cls._databases:
            cls._databases[db_name] = DatabaseManager(db_name)
        return cls._databases[db_name]
    
    @classmethod
    def set_current_database(cls, db_name: str):
        """Set current active database"""
        cls._current_db = db_name
    
    @classmethod
    def get_current_database(cls) -> Optional[DatabaseManager]:
        """Get current active database manager"""
        if cls._current_db:
            return cls.get_database(cls._current_db)
        return None
    
    @classmethod
    def list_databases(cls) -> list:
        """List available databases"""
        # This would typically query the PostgreSQL system to list databases
        # For now, return the configured databases
        return list(cls._databases.keys())
    
    @classmethod
    def close_all(cls):
        """Close all database connections"""
        for db in cls._databases.values():
            db.disconnect()
        cls._databases.clear()
        cls._current_db = None
