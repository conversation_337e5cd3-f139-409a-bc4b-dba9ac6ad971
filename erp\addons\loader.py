"""
Addon loading and management system
"""
import os
import sys
import importlib
import importlib.util
from typing import Dict, List, Optional, Set
from .manifest import AddonManifest
from ..config import config


class AddonLoader:
    """Addon loader and manager"""
    
    def __init__(self):
        self.addons_path = config.addons_path
        self.loaded_addons: Dict[str, AddonManifest] = {}
        self.addon_modules: Dict[str, object] = {}
        self._dependency_graph: Dict[str, Set[str]] = {}
    
    def discover_addons(self) -> Dict[str, AddonManifest]:
        """Discover all available addons"""
        addons = {}
        
        if not os.path.exists(self.addons_path):
            print(f"Addons path {self.addons_path} does not exist")
            return addons
        
        for item in os.listdir(self.addons_path):
            addon_path = os.path.join(self.addons_path, item)
            
            if os.path.isdir(addon_path):
                # Check if it's a valid addon (has __init__.py)
                init_file = os.path.join(addon_path, '__init__.py')
                if os.path.exists(init_file):
                    manifest = AddonManifest(addon_path)
                    if manifest.installable:
                        addons[item] = manifest
        
        return addons
    
    def _build_dependency_graph(self, addons: Dict[str, AddonManifest]):
        """Build dependency graph for addons"""
        self._dependency_graph = {}
        
        for addon_name, manifest in addons.items():
            self._dependency_graph[addon_name] = set(manifest.depends)
    
    def _resolve_dependencies(self, addon_name: str, addons: Dict[str, AddonManifest], 
                            resolved: Set[str], visiting: Set[str]) -> List[str]:
        """Resolve addon dependencies using topological sort"""
        if addon_name in visiting:
            raise ValueError(f"Circular dependency detected involving {addon_name}")
        
        if addon_name in resolved:
            return []
        
        if addon_name not in addons:
            raise ValueError(f"Addon {addon_name} not found")
        
        visiting.add(addon_name)
        load_order = []
        
        # Process dependencies first
        for dep in self._dependency_graph.get(addon_name, set()):
            load_order.extend(self._resolve_dependencies(dep, addons, resolved, visiting))
        
        # Add current addon
        if addon_name not in resolved:
            load_order.append(addon_name)
            resolved.add(addon_name)
        
        visiting.remove(addon_name)
        return load_order
    
    def get_load_order(self, addon_names: List[str] = None) -> List[str]:
        """Get the correct loading order for addons"""
        addons = self.discover_addons()
        self._build_dependency_graph(addons)
        
        if addon_names is None:
            addon_names = list(addons.keys())
        
        resolved = set()
        load_order = []
        
        for addon_name in addon_names:
            if addon_name not in resolved:
                order = self._resolve_dependencies(addon_name, addons, resolved, set())
                load_order.extend(order)
        
        return load_order
    
    def load_addon(self, addon_name: str) -> bool:
        """Load a single addon"""
        if addon_name in self.loaded_addons:
            return True

        addon_path = os.path.join(self.addons_path, addon_name)
        if not os.path.exists(addon_path):
            print(f"Addon {addon_name} not found at {addon_path}")
            return False

        try:
            # Load manifest
            manifest = AddonManifest(addon_path)

            # Add addons path to Python path (not the specific addon path)
            if self.addons_path not in sys.path:
                sys.path.insert(0, self.addons_path)

            # Import the addon module using the addon name as module name
            try:
                module = importlib.import_module(addon_name)
            except ImportError:
                # Fallback to spec-based loading
                spec = importlib.util.spec_from_file_location(
                    addon_name,
                    os.path.join(addon_path, '__init__.py')
                )
                module = importlib.util.module_from_spec(spec)
                sys.modules[addon_name] = module
                spec.loader.exec_module(module)

            # Store loaded addon
            self.loaded_addons[addon_name] = manifest
            self.addon_modules[addon_name] = module

            print(f"Loaded addon: {addon_name}")
            return True

        except Exception as e:
            print(f"Error loading addon {addon_name}: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def load_addons(self, addon_names: List[str] = None) -> bool:
        """Load multiple addons in correct order"""
        try:
            load_order = self.get_load_order(addon_names)
            
            for addon_name in load_order:
                if not self.load_addon(addon_name):
                    print(f"Failed to load addon: {addon_name}")
                    return False
            
            return True
            
        except Exception as e:
            print(f"Error loading addons: {e}")
            return False
    
    def get_loaded_addons(self) -> Dict[str, AddonManifest]:
        """Get all loaded addons"""
        return self.loaded_addons.copy()
    
    def is_addon_loaded(self, addon_name: str) -> bool:
        """Check if addon is loaded"""
        return addon_name in self.loaded_addons
