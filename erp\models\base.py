"""
Base Model class with Odoo-like inheritance and field system
"""
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List, Type
from ..fields import Field, Char, Datetime


class ModelMeta(type):
    """Metaclass for models to handle field definitions"""
    
    def __new__(cls, name, bases, attrs):
        # Collect fields from the class and its parents
        fields = {}
        
        # Get fields from parent classes
        for base in bases:
            if hasattr(base, '_fields'):
                fields.update(base._fields)
        
        # Get fields from current class
        for key, value in list(attrs.items()):
            if isinstance(value, Field):
                fields[key] = value
                # Remove field from class attributes to avoid conflicts
                del attrs[key]
        
        # Store fields in the class
        attrs['_fields'] = fields
        
        # Create the class
        new_class = super().__new__(cls, name, bases, attrs)
        
        # Register the model
        if hasattr(new_class, '_name') and new_class._name:
            ModelRegistry.register(new_class._name, new_class)
        
        return new_class


class ModelRegistry:
    """Registry to store all model classes"""
    _models = {}
    
    @classmethod
    def register(cls, name: str, model_class: Type):
        cls._models[name] = model_class
    
    @classmethod
    def get(cls, name: str) -> Optional[Type]:
        return cls._models.get(name)
    
    @classmethod
    def all(cls) -> Dict[str, Type]:
        return cls._models.copy()


class BaseModel(metaclass=ModelMeta):
    """Base model class with common fields and functionality"""
    
    _name = None  # Model name (to be overridden in subclasses)
    _description = None  # Model description
    _table = None  # Database table name
    
    # Common fields for all models
    id = Char(string='ID', required=True, readonly=True, default=lambda: str(uuid.uuid4()))
    name = Char(string='Name', required=True)
    created_at = Datetime(string='Created At', readonly=True, default=lambda: datetime.now())
    updated_at = Datetime(string='Updated At', readonly=True, default=lambda: datetime.now())
    
    def __init__(self, **kwargs):
        self._values = {}
        
        # Initialize fields with default values
        for field_name, field in self._fields.items():
            if field_name in kwargs:
                self._values[field_name] = kwargs[field_name]
            else:
                default_value = field.get_default_value()
                if default_value is not None:
                    self._values[field_name] = default_value
    
    def __getattr__(self, name):
        """Get field value"""
        if name in self._fields:
            return self._values.get(name)
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")
    
    def __setattr__(self, name, value):
        """Set field value"""
        if name.startswith('_') or name in ['_fields', '_name', '_description', '_table']:
            super().__setattr__(name, value)
        elif hasattr(self, '_fields') and name in self._fields:
            if not hasattr(self, '_values'):
                self._values = {}
            self._values[name] = value
            # Update updated_at timestamp
            if name != 'updated_at':
                self._values['updated_at'] = datetime.now()
        else:
            super().__setattr__(name, value)
    
    @classmethod
    def create(cls, vals: Dict[str, Any]):
        """Create a new record"""
        record = cls(**vals)
        # Here you would typically save to database
        return record
    
    def write(self, vals: Dict[str, Any]):
        """Update record values"""
        for key, value in vals.items():
            if key in self._fields:
                setattr(self, key, value)
    
    def read(self, fields: Optional[List[str]] = None) -> Dict[str, Any]:
        """Read record values"""
        if fields is None:
            fields = list(self._fields.keys())
        
        result = {}
        for field in fields:
            if field in self._fields:
                result[field] = getattr(self, field)
        return result
    
    @classmethod
    def browse(cls, ids: List[str]):
        """Browse records by IDs (placeholder for database integration)"""
        # This would typically fetch from database
        return []
    
    @classmethod
    def search(cls, domain: List = None):
        """Search records (placeholder for database integration)"""
        # This would typically search in database
        return []
    
    def unlink(self):
        """Delete record (placeholder for database integration)"""
        # This would typically delete from database
        pass
    
    @classmethod
    def fields_get(cls) -> Dict[str, Dict[str, Any]]:
        """Get field definitions"""
        result = {}
        for field_name, field in cls._fields.items():
            result[field_name] = {
                'type': field.__class__.__name__.lower(),
                'string': field.string or field_name.replace('_', ' ').title(),
                'required': field.required,
                'readonly': field.readonly,
                'help': field.help,
            }
        return result
    
    def __repr__(self):
        return f"<{self.__class__.__name__}({getattr(self, 'id', 'new')})>"
