"""
Addon manifest handling
"""
import os
import ast
from typing import Dict, Any, List, Optional


class AddonManifest:
    """Addon manifest parser and validator"""
    
    def __init__(self, addon_path: str):
        self.addon_path = addon_path
        self.addon_name = os.path.basename(addon_path)
        self._manifest_data = None
        self._load_manifest()
    
    def _load_manifest(self):
        """Load manifest file"""
        manifest_file = os.path.join(self.addon_path, '__manifest__.py')
        
        if not os.path.exists(manifest_file):
            # Try legacy manifest name
            manifest_file = os.path.join(self.addon_path, '__openerp__.py')
        
        if os.path.exists(manifest_file):
            try:
                with open(manifest_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    # Parse the manifest as Python code
                    self._manifest_data = ast.literal_eval(content)
            except Exception as e:
                print(f"Error loading manifest for {self.addon_name}: {e}")
                self._manifest_data = {}
        else:
            self._manifest_data = {}
    
    @property
    def name(self) -> str:
        """Get addon name"""
        return self._manifest_data.get('name', self.addon_name)
    
    @property
    def version(self) -> str:
        """Get addon version"""
        return self._manifest_data.get('version', '1.0.0')
    
    @property
    def description(self) -> str:
        """Get addon description"""
        return self._manifest_data.get('description', '')
    
    @property
    def author(self) -> str:
        """Get addon author"""
        return self._manifest_data.get('author', '')
    
    @property
    def depends(self) -> List[str]:
        """Get addon dependencies"""
        return self._manifest_data.get('depends', [])
    
    @property
    def data(self) -> List[str]:
        """Get data files"""
        return self._manifest_data.get('data', [])
    
    @property
    def installable(self) -> bool:
        """Check if addon is installable"""
        return self._manifest_data.get('installable', True)
    
    @property
    def auto_install(self) -> bool:
        """Check if addon should be auto-installed"""
        return self._manifest_data.get('auto_install', False)
    
    @property
    def category(self) -> str:
        """Get addon category"""
        return self._manifest_data.get('category', 'Uncategorized')
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert manifest to dictionary"""
        return {
            'name': self.name,
            'version': self.version,
            'description': self.description,
            'author': self.author,
            'depends': self.depends,
            'data': self.data,
            'installable': self.installable,
            'auto_install': self.auto_install,
            'category': self.category,
            'addon_name': self.addon_name,
            'path': self.addon_path,
        }
