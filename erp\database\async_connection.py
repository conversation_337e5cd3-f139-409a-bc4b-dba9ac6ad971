"""
Async database connection and management using asyncpg
"""
import asyncpg
from typing import Dict, Any, Optional, List, Union
from contextlib import asynccontextmanager
from ..config import config


class AsyncDatabaseManager:
    """Async database connection manager with connection pooling"""
    
    def __init__(self, db_name: Optional[str] = None):
        self.db_name = db_name or config.get('options', 'db_name')
        self._pool: Optional[asyncpg.Pool] = None
    
    async def create_pool(self):
        """Create connection pool"""
        if self._pool is None:
            db_config = config.db_config.copy()
            pool_config = config.db_pool_config
            
            if self.db_name:
                db_config['database'] = self.db_name
            
            try:
                self._pool = await asyncpg.create_pool(
                    host=db_config['host'],
                    port=db_config['port'],
                    user=db_config['user'],
                    password=db_config['password'],
                    database=db_config['database'],
                    min_size=pool_config['min_size'],
                    max_size=pool_config['max_size']
                )
            except Exception as e:
                print(f"Database pool creation error: {e}")
                raise
    
    async def close_pool(self):
        """Close connection pool"""
        if self._pool:
            await self._pool.close()
            self._pool = None
    
    @asynccontextmanager
    async def acquire_connection(self):
        """Acquire connection from pool"""
        if not self._pool:
            await self.create_pool()
        
        async with self._pool.acquire() as connection:
            yield connection
    
    async def execute(self, query: str, *params) -> List[Dict[str, Any]]:
        """Execute SQL query"""
        async with self.acquire_connection() as conn:
            try:
                if query.strip().upper().startswith('SELECT'):
                    rows = await conn.fetch(query, *params)
                    return [dict(row) for row in rows]
                else:
                    await conn.execute(query, *params)
                    return []
            except Exception as e:
                print(f"Database query error: {e}")
                raise
    
    async def fetchone(self, query: str, *params) -> Optional[Dict[str, Any]]:
        """Fetch single row"""
        async with self.acquire_connection() as conn:
            try:
                row = await conn.fetchrow(query, *params)
                return dict(row) if row else None
            except Exception as e:
                print(f"Database query error: {e}")
                raise
    
    async def fetchval(self, query: str, *params) -> Any:
        """Fetch single value"""
        async with self.acquire_connection() as conn:
            try:
                return await conn.fetchval(query, *params)
            except Exception as e:
                print(f"Database query error: {e}")
                raise
    
    @asynccontextmanager
    async def transaction(self):
        """Transaction context manager"""
        async with self.acquire_connection() as conn:
            async with conn.transaction():
                yield conn
    
    async def insert(self, table_name: str, data: Dict[str, Any]) -> Optional[Any]:
        """Insert data into table"""
        columns = list(data.keys())
        placeholders = [f'${i+1}' for i in range(len(columns))]
        values = list(data.values())
        
        query = f"""
            INSERT INTO {table_name} ({', '.join(columns)}) 
            VALUES ({', '.join(placeholders)}) 
            RETURNING id
        """
        
        async with self.transaction() as conn:
            return await conn.fetchval(query, *values)
    
    async def update(self, table_name: str, data: Dict[str, Any], 
                    where_clause: str, *where_params) -> int:
        """Update data in table"""
        set_clauses = [f"{col} = ${i+1}" for i, col in enumerate(data.keys())]
        values = list(data.values())
        
        # Adjust parameter numbers for where clause
        where_clause_adjusted = where_clause
        for i, param in enumerate(where_params):
            where_clause_adjusted = where_clause_adjusted.replace(
                f'${i+1}', f'${len(values) + i + 1}'
            )
        
        query = f"""
            UPDATE {table_name} 
            SET {', '.join(set_clauses)} 
            WHERE {where_clause_adjusted}
        """
        
        async with self.transaction() as conn:
            result = await conn.execute(query, *values, *where_params)
            return int(result.split()[-1])  # Extract affected rows count
    
    async def select(self, table_name: str, columns: List[str] = None, 
                    where_clause: str = None, *where_params) -> List[Dict[str, Any]]:
        """Select data from table"""
        cols = ', '.join(columns) if columns else '*'
        query = f"SELECT {cols} FROM {table_name}"
        
        if where_clause:
            query += f" WHERE {where_clause}"
        
        return await self.execute(query, *where_params)
    
    async def delete(self, table_name: str, where_clause: str, *where_params) -> int:
        """Delete data from table"""
        query = f"DELETE FROM {table_name} WHERE {where_clause}"
        
        async with self.transaction() as conn:
            result = await conn.execute(query, *where_params)
            return int(result.split()[-1])  # Extract affected rows count
