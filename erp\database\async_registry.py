"""
Async database registry for managing multiple database connections
"""
from typing import Dict, Optional, List
from .async_connection import AsyncDatabaseManager
from ..config import config


class AsyncDatabaseRegistry:
    """Registry for managing multiple async database connections"""
    
    _databases: Dict[str, AsyncDatabaseManager] = {}
    _current_db: Optional[str] = None
    
    @classmethod
    async def get_database(cls, db_name: str) -> AsyncDatabaseManager:
        """Get database manager for given database name"""
        if db_name not in cls._databases:
            cls._databases[db_name] = AsyncDatabaseManager(db_name)
            await cls._databases[db_name].create_pool()
        return cls._databases[db_name]
    
    @classmethod
    def set_current_database(cls, db_name: str):
        """Set current active database"""
        cls._current_db = db_name
    
    @classmethod
    async def get_current_database(cls) -> Optional[AsyncDatabaseManager]:
        """Get current active database manager"""
        if cls._current_db:
            return await cls.get_database(cls._current_db)
        return None
    
    @classmethod
    async def list_databases(cls) -> List[str]:
        """List available databases"""
        # Get default database manager to query system
        default_db = await cls.get_database('postgres')
        
        try:
            query = """
                SELECT datname FROM pg_database 
                WHERE datistemplate = false 
                AND datname != 'postgres'
                ORDER BY datname
            """
            rows = await default_db.execute(query)
            return [row['datname'] for row in rows]
        except Exception as e:
            print(f"Error listing databases: {e}")
            return []
    
    @classmethod
    async def close_all(cls):
        """Close all database connections"""
        for db_manager in cls._databases.values():
            await db_manager.close_pool()
        cls._databases.clear()
        cls._current_db = None
    
    @classmethod
    async def create_database(cls, db_name: str) -> bool:
        """Create a new database"""
        default_db = await cls.get_database('postgres')
        
        try:
            # Note: Database names cannot be parameterized in PostgreSQL
            # This is safe as long as db_name is validated
            if not db_name.replace('_', '').replace('-', '').isalnum():
                raise ValueError("Invalid database name")
            
            await default_db.execute(f'CREATE DATABASE "{db_name}"')
            return True
        except Exception as e:
            print(f"Error creating database {db_name}: {e}")
            return False
    
    @classmethod
    async def drop_database(cls, db_name: str) -> bool:
        """Drop a database"""
        if db_name in cls._databases:
            await cls._databases[db_name].close_pool()
            del cls._databases[db_name]
        
        default_db = await cls.get_database('postgres')
        
        try:
            # Validate database name for security
            if not db_name.replace('_', '').replace('-', '').isalnum():
                raise ValueError("Invalid database name")
            
            await default_db.execute(f'DROP DATABASE IF EXISTS "{db_name}"')
            return True
        except Exception as e:
            print(f"Error dropping database {db_name}: {e}")
            return False
    
    @classmethod
    async def database_exists(cls, db_name: str) -> bool:
        """Check if database exists"""
        default_db = await cls.get_database('postgres')
        
        try:
            query = "SELECT 1 FROM pg_database WHERE datname = $1"
            result = await default_db.fetchval(query, db_name)
            return result is not None
        except Exception as e:
            print(f"Error checking database existence: {e}")
            return False
