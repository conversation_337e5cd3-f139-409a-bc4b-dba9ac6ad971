#!/usr/bin/env python3
"""
Test script for demo models
"""
import sys
import os

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(__file__))

from erp.addons.loader import AddonLoader
from erp.models.base import ModelRegistry


def test_demo_models():
    """Test demo models functionality"""
    print("Testing Demo Models")
    print("=" * 50)
    
    # Load addons first
    loader = AddonLoader()
    loader.load_addons()
    
    # Get model classes
    customer_model = ModelRegistry.get('demo.customer')
    product_model = ModelRegistry.get('demo.product')
    
    if not customer_model:
        print("✗ Demo customer model not found")
        return
    
    if not product_model:
        print("✗ Demo product model not found")
        return
    
    print("✓ Demo models loaded successfully")
    
    # Test customer creation
    print("\nTesting Customer Model:")
    customer = customer_model.create({
        'name': 'John Doe',
        'email': '<EMAIL>',
        'phone': '******-0123',
        'address': '123 Main St, Anytown, USA',
        'customer_type': 'individual',
        'credit_limit': 5000
    })
    
    print(f"Created customer: {customer.get_display_name()}")
    print(f"Customer data: {customer.read(['name', 'email', 'customer_type', 'credit_limit'])}")
    
    # Test customer methods
    customer.increase_credit_limit(1000)
    print(f"After credit increase: {customer.credit_limit}")
    
    # Test product creation
    print("\nTesting Product Model:")
    product = product_model.create({
        'name': 'Laptop Computer',
        'code': 'LAP001',
        'description': 'High-performance laptop for business use',
        'product_type': 'stockable',
        'category': 'electronics',
        'list_price': 1299.99,
        'cost_price': 899.99,
        'qty_available': 50,
        'weight': 2.5
    })
    
    print(f"Created product: {product.name}")
    print(f"Product data: {product.read(['name', 'code', 'list_price', 'cost_price'])}")
    
    # Test product methods
    margin = product.calculate_margin()
    print(f"Profit margin: {margin:.2f}%")
    
    available_qty = product.get_available_qty()
    print(f"Available quantity: {available_qty}")
    
    # Update stock
    product.update_stock(-5)  # Sell 5 units
    print(f"After selling 5 units: {product.qty_available}")
    
    # Test field definitions
    print("\nTesting Field Definitions:")
    customer_fields = customer_model.fields_get()
    product_fields = product_model.fields_get()
    
    print(f"Customer model has {len(customer_fields)} fields:")
    for field_name, field_def in customer_fields.items():
        print(f"  - {field_name}: {field_def['string']} ({field_def['type']})")
    
    print(f"\nProduct model has {len(product_fields)} fields:")
    for field_name, field_def in list(product_fields.items())[:10]:  # Show first 10
        print(f"  - {field_name}: {field_def['string']} ({field_def['type']})")
    
    print("\n✓ All demo model tests passed!")


if __name__ == '__main__':
    test_demo_models()
