#!/usr/bin/env python3
"""
Async ERP Server Entry Point
"""
import argparse
import sys
import uvicorn
from erp.config import config
from erp.async_server import create_app


def main():
    """Main entry point for async ERP server"""
    parser = argparse.ArgumentParser(description='ERP Async Server')
    parser.add_argument('--host', type=str, help='Host to bind to')
    parser.add_argument('--port', type=int, help='Port to bind to')
    parser.add_argument('--reload', action='store_true', help='Enable auto-reload')
    parser.add_argument('--workers', type=int, default=1, help='Number of worker processes')
    parser.add_argument('--config', type=str, help='Configuration file path')
    parser.add_argument('--db-name', type=str, help='Database name')
    
    args = parser.parse_args()
    
    # Update config if provided
    if args.config:
        config.config_file = args.config
        config._load_config()
    
    if args.db_name:
        config.set('options', 'db_name', args.db_name)
    
    # Get server configuration
    server_config = config.server_config
    host = args.host or server_config['host']
    port = args.port or server_config['port']
    
    print(f"ERP Async Server starting...")
    print(f"Configuration file: {config.config_file}")
    print(f"Database: {config.get('options', 'db_name')}")
    print(f"Addons path: {config.addons_path}")
    print(f"Server: {host}:{port}")
    print(f"Workers: {args.workers}")
    
    try:
        # Create FastAPI app
        app = create_app()
        
        # Run with uvicorn
        uvicorn.run(
            app,
            host=host,
            port=port,
            reload=args.reload,
            workers=args.workers if not args.reload else 1,
            log_level="info",
            access_log=True
        )
    except KeyboardInterrupt:
        print("\nServer stopped by user")
    except Exception as e:
        print(f"Server error: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
