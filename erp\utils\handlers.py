"""
Common request handlers to eliminate duplicate patterns
"""
from typing import Dict, Any, Optional, List, Callable
from fastapi import HTT<PERSON>Exception
from ..models.async_base import AsyncModelRegistry
from .responses import APIResponse, ModelResponse, handle_generic_error


class ModelRequestHandler:
    """Handler for common model operations"""
    
    @staticmethod
    async def get_model_class(model_name: str):
        """Get model class with validation"""
        if not model_name:
            raise APIResponse.validation_error("Missing 'model' parameter")
        
        model_class = AsyncModelRegistry.get(model_name)
        if not model_class:
            raise ModelResponse.model_not_found(model_name)
        
        return model_class
    
    @staticmethod
    async def call_model_method(
        model_name: str, 
        method: str, 
        args: List = None, 
        kwargs: Dict = None
    ):
        """Call method on model class"""
        if not method:
            raise APIResponse.validation_error("Missing 'method' parameter")
        
        args = args or []
        kwargs = kwargs or {}
        
        model_class = await ModelRequestHandler.get_model_class(model_name)
        
        if not hasattr(model_class, method):
            raise ModelResponse.method_not_found(model_name, method)
        
        try:
            method_func = getattr(model_class, method)
            
            # Handle both sync and async methods
            if hasattr(method_func, '__call__'):
                if hasattr(method_func, '__await__'):
                    result = await method_func(*args, **kwargs)
                else:
                    result = method_func(*args, **kwargs)
            else:
                result = method_func
            
            return ModelResponse.model_result(result)
        
        except Exception as e:
            raise handle_generic_error(e)
    
    @staticmethod
    async def search_and_read(
        model_name: str,
        domain: List = None,
        fields: List[str] = None,
        limit: int = 80,
        offset: int = 0
    ):
        """Search and read records"""
        model_class = await ModelRequestHandler.get_model_class(model_name)
        
        try:
            # Search records
            if hasattr(model_class, 'search'):
                search_method = getattr(model_class, 'search')
                if hasattr(search_method, '__await__'):
                    records = await search_method(domain, limit=limit, offset=offset)
                else:
                    records = search_method(domain)[offset:offset+limit]
            else:
                records = []
            
            # Read fields
            result = []
            for record in records:
                if hasattr(record, 'read'):
                    read_method = getattr(record, 'read')
                    if hasattr(read_method, '__await__'):
                        record_data = await read_method(fields)
                    else:
                        record_data = read_method(fields)
                    result.append(record_data)
            
            return APIResponse.success({
                'records': result,
                'length': len(records)
            })
        
        except Exception as e:
            raise handle_generic_error(e)


class RequestValidator:
    """Common request validation utilities"""
    
    @staticmethod
    def validate_required_fields(data: Dict[str, Any], required_fields: List[str]):
        """Validate required fields in request data"""
        missing_fields = []
        for field in required_fields:
            if field not in data or data[field] is None:
                missing_fields.append(field)
        
        if missing_fields:
            raise APIResponse.validation_error(
                f"Missing required fields: {', '.join(missing_fields)}"
            )
    
    @staticmethod
    def validate_field_types(data: Dict[str, Any], field_types: Dict[str, type]):
        """Validate field types in request data"""
        type_errors = []
        for field, expected_type in field_types.items():
            if field in data and data[field] is not None:
                if not isinstance(data[field], expected_type):
                    type_errors.append(
                        f"{field} must be of type {expected_type.__name__}"
                    )
        
        if type_errors:
            raise APIResponse.validation_error(
                f"Type validation errors: {'; '.join(type_errors)}"
            )
    
    @staticmethod
    def extract_pagination(data: Dict[str, Any]) -> Dict[str, int]:
        """Extract pagination parameters"""
        return {
            'limit': data.get('limit', 80),
            'offset': data.get('offset', 0)
        }


class AuthenticationHandler:
    """Authentication utilities"""
    
    @staticmethod
    async def authenticate_user(db: str, login: str, password: str) -> Dict[str, Any]:
        """Authenticate user credentials"""
        from ..config import config
        
        RequestValidator.validate_required_fields(
            {'db': db, 'login': login, 'password': password},
            ['db', 'login', 'password']
        )
        
        # Simple authentication (placeholder)
        admin_passwd = config.get('options', 'admin_passwd', 'admin')
        if login == 'admin' and password == admin_passwd:
            return {
                'uid': 1,
                'username': login,
                'session_id': 'session_123',
                'db': db,
            }
        else:
            raise APIResponse.error('Invalid credentials', 401)
    
    @staticmethod
    async def get_session_info() -> Dict[str, Any]:
        """Get current session information"""
        from ..config import config
        
        return {
            'uid': 1,
            'username': 'admin',
            'db': config.get('options', 'db_name'),
            'server_version': '1.0.0',
            'server_type': 'asgi'
        }
