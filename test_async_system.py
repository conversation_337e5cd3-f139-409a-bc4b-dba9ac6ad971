#!/usr/bin/env python3
"""
Comprehensive async system tests
"""
import asyncio
import pytest
import httpx
import time
from typing import List
from erp.config import config
from erp.async_server import create_app
from erp.database.async_registry import AsyncDatabaseRegistry
from erp.models.async_base import AsyncModelRegistry
from erp.addons.async_loader import AsyncAddonLoader


class AsyncSystemTester:
    """Async system testing utilities"""
    
    def __init__(self):
        self.app = create_app()
        self.base_url = "http://testserver"
        
    async def setup_test_database(self):
        """Setup test database"""
        try:
            # Create test database if it doesn't exist
            test_db_name = "erp_test"
            if not await AsyncDatabaseRegistry.database_exists(test_db_name):
                await AsyncDatabaseRegistry.create_database(test_db_name)
            
            # Set as current database
            AsyncDatabaseRegistry.set_current_database(test_db_name)
            print(f"Test database '{test_db_name}' ready")
            return True
        except Exception as e:
            print(f"Error setting up test database: {e}")
            return False
    
    async def cleanup_test_database(self):
        """Cleanup test database"""
        try:
            test_db_name = "erp_test"
            await AsyncDatabaseRegistry.drop_database(test_db_name)
            print(f"Test database '{test_db_name}' cleaned up")
        except Exception as e:
            print(f"Error cleaning up test database: {e}")
    
    async def test_database_operations(self):
        """Test async database operations"""
        print("\n=== Testing Database Operations ===")
        
        try:
            db = await AsyncDatabaseRegistry.get_current_database()
            
            # Test basic query
            result = await db.fetchval("SELECT 1 as test")
            assert result == 1, "Basic query failed"
            print("✓ Basic query test passed")
            
            # Test transaction
            async with db.transaction() as conn:
                await conn.execute("CREATE TEMP TABLE test_table (id SERIAL, name TEXT)")
                await conn.execute("INSERT INTO test_table (name) VALUES ($1)", "test")
                result = await conn.fetchval("SELECT name FROM test_table WHERE id = 1")
                assert result == "test", "Transaction test failed"
            print("✓ Transaction test passed")
            
            # Test connection pooling
            start_time = time.time()
            tasks = []
            for i in range(10):
                tasks.append(db.fetchval("SELECT $1", i))
            
            results = await asyncio.gather(*tasks)
            end_time = time.time()
            
            assert len(results) == 10, "Connection pooling test failed"
            print(f"✓ Connection pooling test passed ({end_time - start_time:.3f}s for 10 concurrent queries)")
            
            return True
            
        except Exception as e:
            print(f"✗ Database operations test failed: {e}")
            return False
    
    async def test_model_operations(self):
        """Test async model operations"""
        print("\n=== Testing Model Operations ===")
        
        try:
            # Test model registry
            models = AsyncModelRegistry.all()
            assert len(models) > 0, "No models registered"
            print(f"✓ Model registry test passed ({len(models)} models registered)")
            
            # Test model creation and operations
            from erp.models.async_ir_module import AsyncIrModuleModule
            
            # Create a test module record
            test_data = {
                'name': 'test_module',
                'display_name': 'Test Module',
                'state': 'uninstalled',
                'installable': True
            }
            
            # Create table first
            await AsyncIrModuleModule.create_table()
            print("✓ Model table creation test passed")
            
            # Create record
            record = await AsyncIrModuleModule.create(test_data)
            assert record.name == 'test_module', "Record creation failed"
            print("✓ Model record creation test passed")
            
            # Test search
            records = await AsyncIrModuleModule.search([('name', '=', 'test_module')])
            assert len(records) > 0, "Search test failed"
            print("✓ Model search test passed")
            
            # Test update
            await record.write({'state': 'installed'})
            assert record.state == 'installed', "Update test failed"
            print("✓ Model update test passed")
            
            # Test delete
            await record.unlink()
            print("✓ Model delete test passed")
            
            return True
            
        except Exception as e:
            print(f"✗ Model operations test failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def test_api_endpoints(self):
        """Test async API endpoints"""
        print("\n=== Testing API Endpoints ===")
        
        try:
            async with httpx.AsyncClient(app=self.app, base_url=self.base_url) as client:
                # Test root endpoint
                response = await client.get("/")
                assert response.status_code == 200, "Root endpoint failed"
                data = response.json()
                assert data["success"] == True, "Root endpoint response invalid"
                assert "asgi" in data["data"]["server_type"], "Server type not ASGI"
                print("✓ Root endpoint test passed")
                
                # Test addons endpoint
                response = await client.get("/addons")
                assert response.status_code == 200, "Addons endpoint failed"
                print("✓ Addons endpoint test passed")
                
                # Test model call endpoint
                call_data = {
                    "model": "ir.module.module",
                    "method": "fields_get",
                    "args": [],
                    "kwargs": {}
                }
                response = await client.post("/web/dataset/call_kw", json=call_data)
                assert response.status_code == 200, "Model call endpoint failed"
                print("✓ Model call endpoint test passed")
                
                # Test authentication endpoint
                auth_data = {
                    "db": "erp_test",
                    "login": "admin",
                    "password": "admin"
                }
                response = await client.post("/web/session/authenticate", json=auth_data)
                assert response.status_code == 200, "Authentication endpoint failed"
                print("✓ Authentication endpoint test passed")
                
            return True
            
        except Exception as e:
            print(f"✗ API endpoints test failed: {e}")
            return False
    
    async def test_addon_loading(self):
        """Test async addon loading"""
        print("\n=== Testing Addon Loading ===")
        
        try:
            loader = AsyncAddonLoader()
            
            # Test addon discovery
            addons = await loader.discover_addons()
            assert len(addons) > 0, "No addons discovered"
            print(f"✓ Addon discovery test passed ({len(addons)} addons found)")
            
            # Test load order calculation
            load_order = await loader.get_load_order()
            assert len(load_order) > 0, "Load order calculation failed"
            print(f"✓ Load order calculation test passed ({len(load_order)} addons in order)")
            
            # Test addon loading
            success = await loader.load_addons(['base'])
            assert success, "Addon loading failed"
            print("✓ Addon loading test passed")
            
            return True
            
        except Exception as e:
            print(f"✗ Addon loading test failed: {e}")
            return False
    
    async def test_performance(self):
        """Test async performance"""
        print("\n=== Testing Performance ===")
        
        try:
            db = await AsyncDatabaseRegistry.get_current_database()
            
            # Test concurrent database operations
            num_operations = 50
            start_time = time.time()
            
            tasks = []
            for i in range(num_operations):
                tasks.append(db.fetchval("SELECT $1 as value", i))
            
            results = await asyncio.gather(*tasks)
            end_time = time.time()
            
            assert len(results) == num_operations, "Performance test failed"
            
            total_time = end_time - start_time
            ops_per_second = num_operations / total_time
            
            print(f"✓ Performance test passed:")
            print(f"  - {num_operations} concurrent operations in {total_time:.3f}s")
            print(f"  - {ops_per_second:.1f} operations/second")
            
            # Performance should be reasonable (adjust threshold as needed)
            assert ops_per_second > 100, f"Performance too slow: {ops_per_second:.1f} ops/sec"
            
            return True
            
        except Exception as e:
            print(f"✗ Performance test failed: {e}")
            return False
    
    async def run_all_tests(self):
        """Run all async tests"""
        print("Starting Async ERP System Tests...")
        print("=" * 50)
        
        # Setup
        if not await self.setup_test_database():
            print("Failed to setup test database")
            return False
        
        tests = [
            self.test_database_operations,
            self.test_model_operations,
            self.test_addon_loading,
            self.test_api_endpoints,
            self.test_performance,
        ]
        
        passed = 0
        failed = 0
        
        for test in tests:
            try:
                if await test():
                    passed += 1
                else:
                    failed += 1
            except Exception as e:
                print(f"✗ Test {test.__name__} crashed: {e}")
                failed += 1
        
        # Cleanup
        await self.cleanup_test_database()
        await AsyncDatabaseRegistry.close_all()
        
        print("\n" + "=" * 50)
        print(f"Test Results: {passed} passed, {failed} failed")
        
        if failed == 0:
            print("🎉 All async tests passed!")
            return True
        else:
            print("❌ Some tests failed")
            return False


async def main():
    """Main test runner"""
    tester = AsyncSystemTester()
    success = await tester.run_all_tests()
    return 0 if success else 1


if __name__ == '__main__':
    exit_code = asyncio.run(main())
