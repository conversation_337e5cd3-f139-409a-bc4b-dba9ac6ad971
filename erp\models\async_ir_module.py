"""
Async module management models
"""
from .async_base import AsyncBaseModel
from ..fields import Char, Text, Boolean, Selection


class AsyncIrModuleModule(AsyncBaseModel):
    """Async model for managing addon modules"""
    
    _name = 'ir.module.module'
    _description = 'Module'
    _table = 'ir_module_module'
    
    # Override name field to be the technical name
    name = Char(string='Technical Name', required=True, help='Technical name of the module')
    display_name = Char(string='Display Name', required=True, help='Human readable name')
    summary = Char(string='Summary', help='Short description of the module')
    description = Text(string='Description', help='Detailed description of the module')
    author = Char(string='Author', help='Module author')
    version = Char(string='Version', help='Module version')
    category = Char(string='Category', help='Module category')
    website = Char(string='Website', help='Module website')
    
    state = Selection([
        ('uninstalled', 'Not Installed'),
        ('installed', 'Installed'),
        ('to_upgrade', 'To be upgraded'),
        ('to_remove', 'To be removed'),
        ('to_install', 'To be installed'),
    ], string='Status', default='uninstalled', required=True)
    
    installable = Boolean(string='Installable', default=True, 
                         help='Whether this module is installable or not')
    auto_install = Boolean(string='Automatic Installation', default=False,
                          help='An auto-installable module is automatically installed when all its dependencies are satisfied')
    
    async def button_install(self):
        """Install the module"""
        await self.write({'state': 'to_install'})
    
    async def button_uninstall(self):
        """Uninstall the module"""
        await self.write({'state': 'to_remove'})
    
    async def button_upgrade(self):
        """Upgrade the module"""
        await self.write({'state': 'to_upgrade'})
    
    async def button_immediate_install(self):
        """Install the module immediately"""
        await self.write({'state': 'installed'})
    
    async def button_immediate_uninstall(self):
        """Uninstall the module immediately"""
        await self.write({'state': 'uninstalled'})
    
    @classmethod
    async def get_installed_modules(cls):
        """Get list of installed modules"""
        return await cls.search([('state', '=', 'installed')])
    
    @classmethod
    async def get_installable_modules(cls):
        """Get list of installable modules"""
        return await cls.search([('installable', '=', True)])
    
    async def install_dependencies(self):
        """Install module dependencies (placeholder)"""
        # In a real implementation, this would parse dependencies
        # and install them recursively
        pass
