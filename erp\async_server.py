"""
FastAPI ASGI Server for ERP system
"""
from fastapi import <PERSON><PERSON><PERSON>, Request, Depends, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
from typing import Dict, Any, Optional, List
import json

from .config import config
from .database.async_registry import AsyncDatabaseRegistry
from .addons.async_loader import Async<PERSON><PERSON>onLoader
from .models.async_base import AsyncModelRegistry
from .utils import (
    APIResponse, ModelResponse, handle_database_error, handle_generic_error,
    ModelRequestHandler, RequestValidator, AuthenticationHandler,
    timing_middleware, error_handling_middleware, logging_middleware
)


class ERPAsyncServer:
    """Main async ERP server application"""
    
    def __init__(self):
        self.addon_loader = AsyncAddonLoader()
        self.app = self._create_app()
        self._setup_middleware()
        self._setup_routes()
    
    def _create_app(self) -> FastAPI:
        """Create FastAPI application with lifespan management"""
        
        @asynccontextmanager
        async def lifespan(app: FastAPI):
            # Startup
            print("Starting ERP server...")
            await self._load_addons()
            yield
            # Shutdown
            print("Shutting down ERP server...")
            await AsyncDatabaseRegistry.close_all()
        
        return FastAPI(
            title="ERP System",
            description="Odoo-like ERP system with async support",
            version="1.0.0",
            lifespan=lifespan
        )
    
    def _setup_middleware(self):
        """Setup middleware"""
        # CORS middleware
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # Add middleware
        self.app.add_middleware(timing_middleware)
        self.app.add_middleware(error_handling_middleware)
        self.app.add_middleware(logging_middleware)
    
    async def _load_addons(self):
        """Load addons asynchronously"""
        try:
            await self.addon_loader.load_addons()
            print(f"Loaded {len(self.addon_loader.get_loaded_addons())} addons")
        except Exception as e:
            print(f"Error loading addons: {e}")
    
    def _setup_routes(self):
        """Setup FastAPI routes"""
        
        @self.app.get("/")
        async def index():
            """Root endpoint"""
            return APIResponse.success({
                'message': 'ERP System',
                'version': '1.0.0',
                'status': 'running',
                'server_type': 'asgi'
            })
        
        @self.app.get("/web/database/list")
        async def list_databases():
            """List available databases"""
            if not config.list_db:
                raise APIResponse.error('Database listing disabled', 403)
            
            try:
                databases = await AsyncDatabaseRegistry.list_databases()
                return APIResponse.success({'databases': databases})
            except Exception as e:
                raise handle_database_error(e)
        
        @self.app.post("/web/dataset/call_kw")
        async def call_kw(request_data: Dict[str, Any]):
            """Handle model method calls"""
            return await ModelRequestHandler.call_model_method(
                model_name=request_data.get('model'),
                method=request_data.get('method'),
                args=request_data.get('args', []),
                kwargs=request_data.get('kwargs', {})
            )
        
        @self.app.post("/web/dataset/search_read")
        async def search_read(request_data: Dict[str, Any]):
            """Search and read records"""
            return await ModelRequestHandler.search_and_read(
                model_name=request_data.get('model'),
                domain=request_data.get('domain', []),
                fields=request_data.get('fields', []),
                limit=request_data.get('limit', 80),
                offset=request_data.get('offset', 0)
            )
        
        @self.app.post("/web/action/load")
        async def load_action(request_data: Dict[str, Any]):
            """Load action definition"""
            try:
                action_id = request_data.get('action_id')
                if not action_id:
                    raise APIResponse.validation_error("Missing 'action_id' parameter")
                
                # Placeholder implementation
                return APIResponse.success({
                    'id': action_id,
                    'name': 'Sample Action',
                    'type': 'ir.actions.act_window',
                    'res_model': 'sample.model',
                    'view_mode': 'tree,form',
                })
                
            except HTTPException:
                raise
            except Exception as e:
                raise handle_generic_error(e)
        
        @self.app.post("/web/session/authenticate")
        async def authenticate(request_data: Dict[str, Any]):
            """Authenticate user session"""
            result = await AuthenticationHandler.authenticate_user(
                db=request_data.get('db'),
                login=request_data.get('login'),
                password=request_data.get('password')
            )
            return APIResponse.success(result)
        
        @self.app.post("/web/session/get_session_info")
        async def get_session_info():
            """Get session information"""
            result = await AuthenticationHandler.get_session_info()
            return APIResponse.success(result)
        
        @self.app.get("/addons")
        async def list_addons():
            """List loaded addons"""
            try:
                addons = self.addon_loader.get_loaded_addons()
                result = {}
                for name, manifest in addons.items():
                    result[name] = manifest.to_dict()
                return APIResponse.success(result)
            except Exception as e:
                raise handle_generic_error(e)
    
    async def run_async(self, host: str = None, port: int = None):
        """Run the server asynchronously (for testing)"""
        import uvicorn
        server_config = config.server_config
        host = host or server_config['host']
        port = port or server_config['port']
        
        uvicorn_config = uvicorn.Config(
            app=self.app,
            host=host,
            port=port,
            log_level="info"
        )
        server = uvicorn.Server(uvicorn_config)
        await server.serve()


def create_app() -> FastAPI:
    """Create FastAPI application"""
    server = ERPAsyncServer()
    return server.app
