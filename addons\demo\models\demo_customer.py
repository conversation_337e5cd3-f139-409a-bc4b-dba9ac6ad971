"""
Demo Customer model
"""
import sys
import os

# Add the ERP core to Python path
erp_path = os.path.join(os.path.dirname(__file__), '..', '..', '..')
if erp_path not in sys.path:
    sys.path.insert(0, erp_path)

from erp.models.base import BaseModel
from erp.fields import Char, Text, Boolean, Selection, Integer


class DemoCustomer(BaseModel):
    """Demo customer model"""
    
    _name = 'demo.customer'
    _description = 'Demo Customer'
    _table = 'demo_customer'
    
    # Override name field with more specific label
    name = Char(string='Customer Name', required=True, help='Full name of the customer')
    
    # Additional fields
    email = Char(string='Email', size=100, help='Customer email address')
    phone = Char(string='Phone', size=20, help='Customer phone number')
    address = Text(string='Address', help='Customer full address')
    
    customer_type = Selection([
        ('individual', 'Individual'),
        ('company', 'Company'),
        ('government', 'Government'),
    ], string='Customer Type', default='individual', required=True)
    
    active = Boolean(string='Active', default=True, help='Whether the customer is active')
    credit_limit = Integer(string='Credit Limit', default=0, help='Customer credit limit')
    
    def get_display_name(self):
        """Get customer display name"""
        return f"{self.name} ({self.customer_type})"
    
    @classmethod
    def get_active_customers(cls):
        """Get all active customers"""
        # This would typically use database search
        return cls.search([('active', '=', True)])
    
    def set_inactive(self):
        """Set customer as inactive"""
        self.write({'active': False})
    
    def increase_credit_limit(self, amount):
        """Increase customer credit limit"""
        new_limit = (self.credit_limit or 0) + amount
        self.write({'credit_limit': new_limit})
