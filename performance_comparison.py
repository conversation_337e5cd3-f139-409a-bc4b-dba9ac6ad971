#!/usr/bin/env python3
"""
Performance comparison between sync and async implementations
"""
import asyncio
import time
import concurrent.futures
import threading
from typing import List
import psycopg2
import asyncpg
from erp.config import config


class PerformanceComparison:
    """Compare sync vs async database performance"""
    
    def __init__(self):
        self.db_config = config.db_config
        self.num_operations = 100
        
    def sync_database_operation(self, operation_id: int) -> int:
        """Synchronous database operation"""
        try:
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor()
            cursor.execute("SELECT %s as result", (operation_id,))
            result = cursor.fetchone()[0]
            cursor.close()
            conn.close()
            return result
        except Exception as e:
            print(f"Sync operation {operation_id} failed: {e}")
            return -1
    
    async def async_database_operation(self, pool: asyncpg.Pool, operation_id: int) -> int:
        """Asynchronous database operation"""
        try:
            async with pool.acquire() as conn:
                result = await conn.fetchval("SELECT $1 as result", operation_id)
                return result
        except Exception as e:
            print(f"Async operation {operation_id} failed: {e}")
            return -1
    
    def test_sync_sequential(self) -> dict:
        """Test synchronous sequential operations"""
        print(f"Testing {self.num_operations} sync sequential operations...")
        
        start_time = time.time()
        results = []
        
        for i in range(self.num_operations):
            result = self.sync_database_operation(i)
            results.append(result)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        return {
            'type': 'sync_sequential',
            'operations': self.num_operations,
            'total_time': total_time,
            'ops_per_second': self.num_operations / total_time,
            'success_count': len([r for r in results if r >= 0])
        }
    
    def test_sync_threaded(self) -> dict:
        """Test synchronous operations with threading"""
        print(f"Testing {self.num_operations} sync threaded operations...")
        
        start_time = time.time()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [
                executor.submit(self.sync_database_operation, i) 
                for i in range(self.num_operations)
            ]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        end_time = time.time()
        total_time = end_time - start_time
        
        return {
            'type': 'sync_threaded',
            'operations': self.num_operations,
            'total_time': total_time,
            'ops_per_second': self.num_operations / total_time,
            'success_count': len([r for r in results if r >= 0])
        }
    
    async def test_async_concurrent(self) -> dict:
        """Test asynchronous concurrent operations"""
        print(f"Testing {self.num_operations} async concurrent operations...")
        
        # Create connection pool
        pool = await asyncpg.create_pool(
            host=self.db_config['host'],
            port=self.db_config['port'],
            user=self.db_config['user'],
            password=self.db_config['password'],
            database=self.db_config['database'],
            min_size=10,
            max_size=20
        )
        
        start_time = time.time()
        
        # Create concurrent tasks
        tasks = [
            self.async_database_operation(pool, i) 
            for i in range(self.num_operations)
        ]
        results = await asyncio.gather(*tasks)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        await pool.close()
        
        return {
            'type': 'async_concurrent',
            'operations': self.num_operations,
            'total_time': total_time,
            'ops_per_second': self.num_operations / total_time,
            'success_count': len([r for r in results if r >= 0])
        }
    
    def test_memory_usage(self) -> dict:
        """Test memory usage comparison"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        
        # Baseline memory
        baseline_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Test sync memory usage
        print("Testing sync memory usage...")
        connections = []
        try:
            for i in range(50):
                conn = psycopg2.connect(**self.db_config)
                connections.append(conn)
            
            sync_memory = process.memory_info().rss / 1024 / 1024  # MB
            sync_memory_per_conn = (sync_memory - baseline_memory) / 50
            
        finally:
            for conn in connections:
                conn.close()
        
        return {
            'baseline_memory_mb': baseline_memory,
            'sync_memory_mb': sync_memory,
            'sync_memory_per_connection_mb': sync_memory_per_conn,
        }
    
    async def run_comparison(self):
        """Run complete performance comparison"""
        print("=" * 60)
        print("ERP System Performance Comparison: Sync vs Async")
        print("=" * 60)
        
        results = []
        
        # Test sync sequential
        sync_seq_result = self.test_sync_sequential()
        results.append(sync_seq_result)
        
        # Test sync threaded
        sync_thread_result = self.test_sync_threaded()
        results.append(sync_thread_result)
        
        # Test async concurrent
        async_result = await self.test_async_concurrent()
        results.append(async_result)
        
        # Test memory usage
        memory_result = self.test_memory_usage()
        
        # Print results
        print("\n" + "=" * 60)
        print("PERFORMANCE RESULTS")
        print("=" * 60)
        
        for result in results:
            print(f"\n{result['type'].upper()}:")
            print(f"  Operations: {result['operations']}")
            print(f"  Total time: {result['total_time']:.3f}s")
            print(f"  Ops/second: {result['ops_per_second']:.1f}")
            print(f"  Success rate: {result['success_count']}/{result['operations']} ({result['success_count']/result['operations']*100:.1f}%)")
        
        print(f"\nMEMORY USAGE:")
        print(f"  Baseline: {memory_result['baseline_memory_mb']:.1f} MB")
        print(f"  Sync (50 connections): {memory_result['sync_memory_mb']:.1f} MB")
        print(f"  Memory per sync connection: {memory_result['sync_memory_per_connection_mb']:.2f} MB")
        
        # Calculate improvements
        sync_best = max(sync_seq_result['ops_per_second'], sync_thread_result['ops_per_second'])
        async_ops = async_result['ops_per_second']
        improvement = (async_ops / sync_best - 1) * 100
        
        print("\n" + "=" * 60)
        print("SUMMARY")
        print("=" * 60)
        print(f"Best sync performance: {sync_best:.1f} ops/sec")
        print(f"Async performance: {async_ops:.1f} ops/sec")
        print(f"Performance improvement: {improvement:+.1f}%")
        
        if improvement > 0:
            print("🚀 Async implementation is faster!")
        else:
            print("⚠️  Sync implementation is faster (check configuration)")
        
        return results


async def main():
    """Main comparison runner"""
    try:
        comparison = PerformanceComparison()
        await comparison.run_comparison()
    except Exception as e:
        print(f"Error running performance comparison: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    asyncio.run(main())
