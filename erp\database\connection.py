"""
Database connection and management
"""
import psycopg2
import psycopg2.extras
from typing import Dict, Any, Optional, List
from ..config import config


class DatabaseManager:
    """Database connection manager"""
    
    def __init__(self, db_name: Optional[str] = None):
        self.db_name = db_name or config.get('options', 'db_name')
        self._connection = None
        self._cursor = None
    
    def connect(self):
        """Connect to database"""
        if self._connection is None:
            db_config = config.db_config.copy()
            if self.db_name:
                db_config['database'] = self.db_name
            
            try:
                self._connection = psycopg2.connect(**db_config)
                self._connection.autocommit = False
                self._cursor = self._connection.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            except psycopg2.Error as e:
                print(f"Database connection error: {e}")
                raise
    
    def disconnect(self):
        """Disconnect from database"""
        if self._cursor:
            self._cursor.close()
            self._cursor = None
        if self._connection:
            self._connection.close()
            self._connection = None
    
    def execute(self, query: str, params: Optional[tuple] = None) -> List[Dict[str, Any]]:
        """Execute SQL query"""
        self.connect()
        try:
            self._cursor.execute(query, params)
            if query.strip().upper().startswith('SELECT'):
                return [dict(row) for row in self._cursor.fetchall()]
            return []
        except psycopg2.Error as e:
            self._connection.rollback()
            print(f"Database query error: {e}")
            raise
    
    def commit(self):
        """Commit transaction"""
        if self._connection:
            self._connection.commit()
    
    def rollback(self):
        """Rollback transaction"""
        if self._connection:
            self._connection.rollback()
    
    def create_table(self, table_name: str, columns: Dict[str, str]):
        """Create table with given columns"""
        column_defs = []
        for col_name, col_type in columns.items():
            column_defs.append(f"{col_name} {col_type}")
        
        query = f"CREATE TABLE IF NOT EXISTS {table_name} ({', '.join(column_defs)})"
        self.execute(query)
        self.commit()
    
    def table_exists(self, table_name: str) -> bool:
        """Check if table exists"""
        query = """
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = %s
            )
        """
        result = self.execute(query, (table_name,))
        return result[0]['exists'] if result else False
    
    def insert(self, table_name: str, data: Dict[str, Any]) -> str:
        """Insert data into table"""
        columns = list(data.keys())
        values = list(data.values())
        placeholders = ', '.join(['%s'] * len(values))
        
        query = f"INSERT INTO {table_name} ({', '.join(columns)}) VALUES ({placeholders}) RETURNING id"
        result = self.execute(query, tuple(values))
        self.commit()
        return result[0]['id'] if result else None
    
    def update(self, table_name: str, data: Dict[str, Any], where_clause: str, where_params: tuple = None):
        """Update data in table"""
        set_clause = ', '.join([f"{col} = %s" for col in data.keys()])
        values = list(data.values())
        
        query = f"UPDATE {table_name} SET {set_clause} WHERE {where_clause}"
        params = tuple(values) + (where_params or ())
        self.execute(query, params)
        self.commit()
    
    def select(self, table_name: str, columns: List[str] = None, where_clause: str = None, 
               where_params: tuple = None) -> List[Dict[str, Any]]:
        """Select data from table"""
        cols = ', '.join(columns) if columns else '*'
        query = f"SELECT {cols} FROM {table_name}"
        
        if where_clause:
            query += f" WHERE {where_clause}"
        
        return self.execute(query, where_params)
    
    def delete(self, table_name: str, where_clause: str, where_params: tuple = None):
        """Delete data from table"""
        query = f"DELETE FROM {table_name} WHERE {where_clause}"
        self.execute(query, where_params)
        self.commit()
    
    def __enter__(self):
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type:
            self.rollback()
        else:
            self.commit()
        self.disconnect()
