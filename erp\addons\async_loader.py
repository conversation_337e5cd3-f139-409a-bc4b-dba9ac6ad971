"""
Async addon loading and management system
"""
import os
import sys
import importlib
import importlib.util
import asyncio
from typing import Dict, List, Optional, Set, Callable, Any
from .manifest import AddonManifest
from ..config import config
from ..database.async_registry import AsyncDatabaseRegistry


class AsyncAddonLoader:
    """Async addon loader and manager"""
    
    def __init__(self):
        self.addons_path = config.addons_path
        self.loaded_addons: Dict[str, AddonManifest] = {}
        self.addon_modules: Dict[str, object] = {}
        self._dependency_graph: Dict[str, Set[str]] = {}
        self._initialization_hooks: Dict[str, List[Callable]] = {}
    
    async def discover_addons(self) -> Dict[str, AddonManifest]:
        """Discover all available addons asynchronously"""
        addons = {}
        
        if not os.path.exists(self.addons_path):
            print(f"Addons path {self.addons_path} does not exist")
            return addons
        
        # Run directory scanning in executor to avoid blocking
        loop = asyncio.get_event_loop()
        items = await loop.run_in_executor(None, os.listdir, self.addons_path)
        
        for item in items:
            addon_path = os.path.join(self.addons_path, item)
            
            if await loop.run_in_executor(None, os.path.isdir, addon_path):
                # Check if it's a valid addon (has __init__.py)
                init_file = os.path.join(addon_path, '__init__.py')
                if await loop.run_in_executor(None, os.path.exists, init_file):
                    manifest = AddonManifest(addon_path)
                    if manifest.installable:
                        addons[item] = manifest
        
        return addons
    
    def _build_dependency_graph(self, addons: Dict[str, AddonManifest]):
        """Build dependency graph for addons"""
        self._dependency_graph = {}
        
        for addon_name, manifest in addons.items():
            self._dependency_graph[addon_name] = set(manifest.depends)
    
    def _resolve_dependencies(self, addon_name: str, addons: Dict[str, AddonManifest], 
                            resolved: Set[str], visiting: Set[str]) -> List[str]:
        """Resolve addon dependencies using topological sort"""
        if addon_name in visiting:
            raise ValueError(f"Circular dependency detected involving {addon_name}")
        
        if addon_name in resolved:
            return []
        
        if addon_name not in addons:
            raise ValueError(f"Addon {addon_name} not found")
        
        visiting.add(addon_name)
        load_order = []
        
        # Process dependencies first
        for dep in self._dependency_graph.get(addon_name, set()):
            load_order.extend(self._resolve_dependencies(dep, addons, resolved, visiting))
        
        # Add current addon
        if addon_name not in resolved:
            load_order.append(addon_name)
            resolved.add(addon_name)
        
        visiting.remove(addon_name)
        return load_order
    
    async def get_load_order(self, addon_names: List[str] = None) -> List[str]:
        """Get the correct loading order for addons"""
        addons = await self.discover_addons()
        self._build_dependency_graph(addons)
        
        if addon_names is None:
            addon_names = list(addons.keys())
        
        resolved = set()
        load_order = []
        
        for addon_name in addon_names:
            if addon_name not in resolved:
                order = self._resolve_dependencies(addon_name, addons, resolved, set())
                load_order.extend(order)
        
        return load_order
    
    async def _create_addon_tables(self, addon_name: str, manifest: AddonManifest):
        """Create database tables for addon models"""
        try:
            # Import models to trigger registration
            models_path = os.path.join(self.addons_path, addon_name, 'models')
            if os.path.exists(models_path):
                # Import all model files
                for file in os.listdir(models_path):
                    if file.endswith('.py') and file != '__init__.py':
                        module_name = f"{addon_name}.models.{file[:-3]}"
                        try:
                            importlib.import_module(module_name)
                        except ImportError as e:
                            print(f"Warning: Could not import {module_name}: {e}")
                
                # Create tables for registered models
                from ..models.async_base import AsyncModelRegistry
                for model_name, model_class in AsyncModelRegistry.all().items():
                    if hasattr(model_class, 'create_table'):
                        try:
                            await model_class.create_table()
                            print(f"Created table for model: {model_name}")
                        except Exception as e:
                            print(f"Warning: Could not create table for {model_name}: {e}")
        
        except Exception as e:
            print(f"Error creating tables for addon {addon_name}: {e}")
    
    async def _run_initialization_hooks(self, addon_name: str):
        """Run async initialization hooks for addon"""
        hooks = self._initialization_hooks.get(addon_name, [])
        for hook in hooks:
            try:
                if asyncio.iscoroutinefunction(hook):
                    await hook()
                else:
                    hook()
            except Exception as e:
                print(f"Error running initialization hook for {addon_name}: {e}")
    
    def register_initialization_hook(self, addon_name: str, hook: Callable):
        """Register an initialization hook for an addon"""
        if addon_name not in self._initialization_hooks:
            self._initialization_hooks[addon_name] = []
        self._initialization_hooks[addon_name].append(hook)
    
    async def load_addon(self, addon_name: str) -> bool:
        """Load a single addon asynchronously"""
        if addon_name in self.loaded_addons:
            return True

        addon_path = os.path.join(self.addons_path, addon_name)
        loop = asyncio.get_event_loop()
        
        if not await loop.run_in_executor(None, os.path.exists, addon_path):
            print(f"Addon {addon_name} not found at {addon_path}")
            return False

        try:
            # Load manifest
            manifest = AddonManifest(addon_path)

            # Add addons path to Python path (not the specific addon path)
            if self.addons_path not in sys.path:
                sys.path.insert(0, self.addons_path)

            # Import the addon module using the addon name as module name
            try:
                module = importlib.import_module(addon_name)
            except ImportError:
                # Fallback to spec-based loading
                spec = importlib.util.spec_from_file_location(
                    addon_name,
                    os.path.join(addon_path, '__init__.py')
                )
                module = importlib.util.module_from_spec(spec)
                sys.modules[addon_name] = module
                spec.loader.exec_module(module)

            # Create database tables for addon models
            await self._create_addon_tables(addon_name, manifest)
            
            # Run initialization hooks
            await self._run_initialization_hooks(addon_name)

            # Store loaded addon
            self.loaded_addons[addon_name] = manifest
            self.addon_modules[addon_name] = module

            print(f"Loaded addon: {addon_name}")
            return True

        except Exception as e:
            print(f"Error loading addon {addon_name}: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def load_addons(self, addon_names: List[str] = None) -> bool:
        """Load multiple addons in correct order asynchronously"""
        try:
            load_order = await self.get_load_order(addon_names)
            
            # Load addons sequentially to respect dependencies
            for addon_name in load_order:
                if not await self.load_addon(addon_name):
                    print(f"Failed to load addon: {addon_name}")
                    return False
            
            return True
            
        except Exception as e:
            print(f"Error loading addons: {e}")
            return False
    
    async def unload_addon(self, addon_name: str) -> bool:
        """Unload an addon"""
        if addon_name not in self.loaded_addons:
            return True
        
        try:
            # Remove from loaded addons
            del self.loaded_addons[addon_name]
            
            # Remove module from sys.modules if it exists
            if addon_name in sys.modules:
                del sys.modules[addon_name]
            
            # Remove from addon modules
            if addon_name in self.addon_modules:
                del self.addon_modules[addon_name]
            
            print(f"Unloaded addon: {addon_name}")
            return True
            
        except Exception as e:
            print(f"Error unloading addon {addon_name}: {e}")
            return False
    
    def get_loaded_addons(self) -> Dict[str, AddonManifest]:
        """Get all loaded addons"""
        return self.loaded_addons.copy()
    
    def is_addon_loaded(self, addon_name: str) -> bool:
        """Check if addon is loaded"""
        return addon_name in self.loaded_addons
    
    async def reload_addon(self, addon_name: str) -> bool:
        """Reload an addon"""
        if await self.unload_addon(addon_name):
            return await self.load_addon(addon_name)
        return False
