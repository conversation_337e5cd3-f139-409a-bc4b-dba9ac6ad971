"""
Field definitions for ERP models - Odoo-like field system
"""
import uuid
from datetime import datetime
from typing import Any, Optional, Dict, List


class Field:
    """Base field class"""
    
    def __init__(self, string=None, required=False, readonly=False, default=None, 
                 help=None, index=False, **kwargs):
        self.string = string
        self.required = required
        self.readonly = readonly
        self.default = default
        self.help = help
        self.index = index
        self.kwargs = kwargs
        
    def get_default_value(self):
        """Get the default value for this field"""
        if callable(self.default):
            return self.default()
        return self.default

    def get_sql_type(self):
        """Get SQL type for this field"""
        return "TEXT"


class Char(Field):
    """Character field"""

    def __init__(self, size=None, **kwargs):
        super().__init__(**kwargs)
        self.size = size

    def get_sql_type(self):
        if self.size:
            return f"VARCHAR({self.size})"
        return "TEXT"


class Text(Field):
    """Text field for longer content"""

    def get_sql_type(self):
        return "TEXT"


class Integer(Field):
    """Integer field"""

    def get_sql_type(self):
        return "INTEGER"


class Float(Field):
    """Float field"""

    def __init__(self, digits=None, **kwargs):
        super().__init__(**kwargs)
        self.digits = digits

    def get_sql_type(self):
        return "REAL"


class Boolean(Field):
    """Boolean field"""

    def get_sql_type(self):
        return "BOOLEAN"


class Date(Field):
    """Date field"""

    def get_sql_type(self):
        return "DATE"


class Datetime(Field):
    """Datetime field"""

    def __init__(self, **kwargs):
        if 'default' not in kwargs:
            kwargs['default'] = lambda: datetime.now()
        super().__init__(**kwargs)

    def get_sql_type(self):
        return "TIMESTAMP"


class Selection(Field):
    """Selection field"""

    def __init__(self, selection, **kwargs):
        super().__init__(**kwargs)
        self.selection = selection

    def get_sql_type(self):
        return "VARCHAR(255)"


class Many2one(Field):
    """Many to one relationship field"""

    def __init__(self, comodel_name, **kwargs):
        super().__init__(**kwargs)
        self.comodel_name = comodel_name

    def get_sql_type(self):
        return "VARCHAR(255)"  # Store as foreign key reference


class One2many(Field):
    """One to many relationship field"""

    def __init__(self, comodel_name, inverse_name, **kwargs):
        super().__init__(**kwargs)
        self.comodel_name = comodel_name
        self.inverse_name = inverse_name

    def get_sql_type(self):
        return None  # One2many fields are not stored directly


class Many2many(Field):
    """Many to many relationship field"""

    def __init__(self, comodel_name, **kwargs):
        super().__init__(**kwargs)
        self.comodel_name = comodel_name

    def get_sql_type(self):
        return None  # Many2many fields use junction tables
