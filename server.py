#!/usr/bin/env python3
"""
ERP Server Entry Point
"""
import sys
import os
import argparse

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(__file__))

from erp.server import ERPServer
from erp.config import config


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='ERP Server')
    parser.add_argument('--config', '-c', help='Configuration file path')
    parser.add_argument('--port', '-p', type=int, help='Server port')
    parser.add_argument('--host', help='Server host')
    parser.add_argument('--debug', action='store_true', help='Enable debug mode')
    parser.add_argument('--db-name', help='Database name')
    
    args = parser.parse_args()
    
    # Update config if arguments provided
    if args.config:
        config.config_file = args.config
        config._load_config()
    
    if args.db_name:
        config.set('options', 'db_name', args.db_name)
    
    # Create and run server
    server = ERPServer()
    
    host = args.host or config.server_config['host']
    port = args.port or config.server_config['port']
    
    print(f"ERP Server starting...")
    print(f"Configuration file: {config.config_file}")
    print(f"Database: {config.get('options', 'db_name')}")
    print(f"Addons path: {config.addons_path}")
    
    try:
        server.run(host=host, port=port, debug=args.debug)
    except KeyboardInterrupt:
        print("\nServer stopped by user")
    except Exception as e:
        print(f"Server error: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
