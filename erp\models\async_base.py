"""
Async base model with database integration
"""
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List, Type, Union
from ..fields import Field
from ..database.async_registry import AsyncDatabaseRegistry


class AsyncModelMeta(type):
    """Metaclass for async models to handle field definitions"""
    
    def __new__(cls, name, bases, attrs):
        # Collect fields from the class and its parents
        fields = {}
        
        # Get fields from parent classes
        for base in bases:
            if hasattr(base, '_fields'):
                fields.update(base._fields)
        
        # Get fields from current class
        for key, value in list(attrs.items()):
            if isinstance(value, Field):
                fields[key] = value
                # Remove field from class attributes to avoid conflicts
                del attrs[key]
        
        # Store fields in the class
        attrs['_fields'] = fields
        
        # Create the class
        new_class = super().__new__(cls, name, bases, attrs)
        
        # Register the model
        if hasattr(new_class, '_name') and new_class._name:
            AsyncModelRegistry.register(new_class._name, new_class)
        
        return new_class


class AsyncModelRegistry:
    """Registry to store all async model classes"""
    _models = {}
    
    @classmethod
    def register(cls, name: str, model_class: Type):
        cls._models[name] = model_class
    
    @classmethod
    def get(cls, name: str) -> Optional[Type]:
        return cls._models.get(name)
    
    @classmethod
    def all(cls) -> Dict[str, Type]:
        return cls._models.copy()


class AsyncBaseModel(metaclass=AsyncModelMeta):
    """Async base model class with database integration"""
    
    _name = None  # Model name (to be overridden in subclasses)
    _description = None  # Model description
    _table = None  # Database table name
    
    # Common fields for all models
    from ..fields import Char, Datetime
    id = Char(string='ID', required=True, readonly=True, default=lambda: str(uuid.uuid4()))
    name = Char(string='Name', required=True)
    created_at = Datetime(string='Created At', readonly=True, default=lambda: datetime.now())
    updated_at = Datetime(string='Updated At', readonly=True, default=lambda: datetime.now())
    
    def __init__(self, **kwargs):
        self._values = {}
        self._is_new_record = True
        
        # Initialize fields with default values
        for field_name, field in self._fields.items():
            if field_name in kwargs:
                self._values[field_name] = kwargs[field_name]
            else:
                default_value = field.get_default_value()
                if default_value is not None:
                    self._values[field_name] = default_value
    
    def __getattr__(self, name):
        """Get field value"""
        if name in self._fields:
            return self._values.get(name)
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")
    
    def __setattr__(self, name, value):
        """Set field value"""
        if name.startswith('_') or name in ['_fields', '_name', '_description', '_table']:
            super().__setattr__(name, value)
        elif hasattr(self, '_fields') and name in self._fields:
            if not hasattr(self, '_values'):
                self._values = {}
            self._values[name] = value
            # Update updated_at timestamp
            if name != 'updated_at':
                self._values['updated_at'] = datetime.now()
        else:
            super().__setattr__(name, value)
    
    @classmethod
    async def create(cls, vals: Dict[str, Any]):
        """Create a new record in database"""
        record = cls(**vals)
        
        # Get database manager
        db = await AsyncDatabaseRegistry.get_current_database()
        if not db:
            raise RuntimeError("No database connection available")
        
        # Prepare data for insertion
        insert_data = {}
        for field_name, field in cls._fields.items():
            if field_name in record._values:
                insert_data[field_name] = record._values[field_name]
        
        # Insert into database
        table_name = cls._table or cls._name.replace('.', '_')
        record_id = await db.insert(table_name, insert_data)
        
        if record_id:
            record._values['id'] = str(record_id)
        
        record._is_new_record = False
        return record
    
    async def write(self, vals: Dict[str, Any]):
        """Update record values in database"""
        for key, value in vals.items():
            if key in self._fields:
                setattr(self, key, value)
        
        if not self._is_new_record and 'id' in self._values:
            # Get database manager
            db = await AsyncDatabaseRegistry.get_current_database()
            if not db:
                raise RuntimeError("No database connection available")
            
            # Prepare data for update
            update_data = {}
            for field_name, field in self._fields.items():
                if field_name in vals and field_name != 'id':
                    update_data[field_name] = self._values[field_name]
            
            # Update in database
            table_name = self._table or self._name.replace('.', '_')
            await db.update(table_name, update_data, 'id = $1', self._values['id'])
    
    def read(self, fields: Optional[List[str]] = None) -> Dict[str, Any]:
        """Read record values"""
        if fields is None:
            fields = list(self._fields.keys())
        
        result = {}
        for field in fields:
            if field in self._fields:
                result[field] = getattr(self, field)
        return result
    
    @classmethod
    async def browse(cls, ids: Union[str, List[str]]):
        """Browse records by IDs from database"""
        if isinstance(ids, str):
            ids = [ids]
        
        # Get database manager
        db = await AsyncDatabaseRegistry.get_current_database()
        if not db:
            raise RuntimeError("No database connection available")
        
        # Query database
        table_name = cls._table or cls._name.replace('.', '_')
        placeholders = ', '.join([f'${i+1}' for i in range(len(ids))])
        query = f"SELECT * FROM {table_name} WHERE id IN ({placeholders})"
        
        rows = await db.execute(query, *ids)
        
        # Create record instances
        records = []
        for row in rows:
            record = cls(**row)
            record._is_new_record = False
            records.append(record)
        
        return records
    
    @classmethod
    async def search(cls, domain: List = None, limit: int = None, offset: int = 0):
        """Search records from database"""
        # Get database manager
        db = await AsyncDatabaseRegistry.get_current_database()
        if not db:
            raise RuntimeError("No database connection available")
        
        # Build query (simplified domain handling)
        table_name = cls._table or cls._name.replace('.', '_')
        query = f"SELECT * FROM {table_name}"
        params = []
        
        # Simple domain processing (placeholder)
        if domain:
            # This is a simplified implementation
            # In a real system, you'd parse the domain properly
            query += " WHERE 1=1"
        
        # Add limit and offset
        if limit:
            query += f" LIMIT {limit}"
        if offset:
            query += f" OFFSET {offset}"
        
        rows = await db.execute(query, *params)
        
        # Create record instances
        records = []
        for row in rows:
            record = cls(**row)
            record._is_new_record = False
            records.append(record)
        
        return records
    
    async def unlink(self):
        """Delete record from database"""
        if not self._is_new_record and 'id' in self._values:
            # Get database manager
            db = await AsyncDatabaseRegistry.get_current_database()
            if not db:
                raise RuntimeError("No database connection available")
            
            # Delete from database
            table_name = self._table or self._name.replace('.', '_')
            await db.delete(table_name, 'id = $1', self._values['id'])
    
    @classmethod
    def fields_get(cls) -> Dict[str, Dict[str, Any]]:
        """Get field definitions"""
        result = {}
        for field_name, field in cls._fields.items():
            result[field_name] = {
                'type': field.__class__.__name__.lower(),
                'string': field.string or field_name.replace('_', ' ').title(),
                'required': field.required,
                'readonly': field.readonly,
                'help': field.help,
            }
        return result
    
    @classmethod
    async def create_table(cls):
        """Create database table for this model"""
        # Get database manager
        db = await AsyncDatabaseRegistry.get_current_database()
        if not db:
            raise RuntimeError("No database connection available")
        
        table_name = cls._table or cls._name.replace('.', '_')
        
        # Build CREATE TABLE statement
        columns = []
        for field_name, field in cls._fields.items():
            column_def = f"{field_name} {field.get_sql_type()}"
            if field.required:
                column_def += " NOT NULL"
            columns.append(column_def)
        
        # Add primary key
        columns.append("PRIMARY KEY (id)")
        
        create_sql = f"""
            CREATE TABLE IF NOT EXISTS {table_name} (
                {', '.join(columns)}
            )
        """
        
        await db.execute(create_sql)
    
    def __repr__(self):
        return f"<{self.__class__.__name__}({getattr(self, 'id', 'new')})>"
